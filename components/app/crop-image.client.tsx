"use client";

import { useState, useCallback } from "react";
import { <PERSON><PERSON>, buttonVariants } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { ImageIcon, MousePointerClickIcon, XIcon, DownloadIcon, RotateCcwIcon } from "lucide-react";
import { cn } from "@/lib/utils";
import { Dropzone, DropzoneContent, DropzoneEmptyState } from "@/components/ui/kibo-ui/dropzone";
import { ImageCrop, ImageCropApply, ImageCropContent, ImageCropReset } from "@/components/ui/kibo-ui/image-crop";
import type { PercentCrop, PixelCrop } from "react-image-crop";

// 常见宽高比选项
const ASPECT_RATIOS = [
	{ label: "Free", value: "free", ratio: undefined },
	{ label: "1:1 (Square)", value: "1:1", ratio: 1 },
	{ label: "4:3", value: "4:3", ratio: 4 / 3 },
	{ label: "3:4", value: "3:4", ratio: 3 / 4 },
	{ label: "16:9", value: "16:9", ratio: 16 / 9 },
	{ label: "9:16", value: "9:16", ratio: 9 / 16 },
	{ label: "3:2", value: "3:2", ratio: 3 / 2 },
	{ label: "2:3", value: "2:3", ratio: 2 / 3 },
];

export function CropImageClient({ hookText = "or, drag and drop an image here" }: { hookText?: string }) {
	const [selectedFile, setSelectedFile] = useState<File | null>(null);
	const [croppedImage, setCroppedImage] = useState<string | null>(null);

	// 裁剪相关状态
	const [crop, setCrop] = useState<PercentCrop>();
	const [completedCrop, setCompletedCrop] = useState<PixelCrop | null>(null);
	const [aspectRatio, setAspectRatio] = useState<number | undefined>(1);
	const [selectedAspectRatio, setSelectedAspectRatio] = useState("1:1");

	// 图片尺寸状态
	const [imageSize, setImageSize] = useState({ width: 0, height: 0 });
	const [cropSize, setCropSize] = useState({ width: 0, height: 0 });
	const [cropPosition, setCropPosition] = useState({ x: 0, y: 0 });

	const handleLocalFileDrop = async (files: File[]) => {
		if (!files || files.length === 0) return;
		setSelectedFile(files[0]);
		setCroppedImage(null);
	};

	const handleReset = () => {
		setSelectedFile(null);
		setCroppedImage(null);
		setCrop(undefined);
		setCompletedCrop(null);
		setAspectRatio(1);
		setSelectedAspectRatio("1:1");
		setImageSize({ width: 0, height: 0 });
		setCropSize({ width: 0, height: 0 });
		setCropPosition({ x: 0, y: 0 });
	};

	// 处理裁剪变化
	const handleCropChange = useCallback(
		(pixelCrop: PixelCrop, percentCrop: PercentCrop) => {
			setCrop(percentCrop);
			if (pixelCrop) {
				// 从pixelCrop和percentCrop推断图片尺寸
				if (percentCrop.width > 0 && percentCrop.height > 0) {
					const imgWidth = Math.round(pixelCrop.width / (percentCrop.width / 100));
					const imgHeight = Math.round(pixelCrop.height / (percentCrop.height / 100));

					if (imgWidth !== imageSize.width || imgHeight !== imageSize.height) {
						setImageSize({ width: imgWidth, height: imgHeight });
					}
				}

				setCropSize({
					width: Math.round(pixelCrop.width),
					height: Math.round(pixelCrop.height),
				});
				setCropPosition({
					x: Math.round(pixelCrop.x),
					y: Math.round(pixelCrop.y),
				});
			}
		},
		[imageSize],
	);

	// 处理裁剪完成
	const handleCropComplete = useCallback(async (pixelCrop: PixelCrop) => {
		setCompletedCrop(pixelCrop);

		// 更新裁剪尺寸和位置
		setCropSize({
			width: Math.round(pixelCrop.width),
			height: Math.round(pixelCrop.height),
		});
		setCropPosition({
			x: Math.round(pixelCrop.x),
			y: Math.round(pixelCrop.y),
		});
	}, []);

	// 处理宽高比变化
	const handleAspectRatioChange = (value: string) => {
		setSelectedAspectRatio(value);
		const selected = ASPECT_RATIOS.find((ar) => ar.value === value);
		setAspectRatio(selected?.ratio);
	};

	// 处理裁剪尺寸变化
	const handleCropSizeChange = (field: "width" | "height", value: string) => {
		const numValue = parseInt(value) || 0;
		if (numValue < 0 || numValue > (field === "width" ? imageSize.width : imageSize.height)) return;

		setCropSize((prev) => ({ ...prev, [field]: numValue }));

		// 更新裁剪区域
		if (crop && imageSize.width && imageSize.height) {
			const newCrop = { ...crop };
			if (field === "width") {
				newCrop.width = (numValue / imageSize.width) * 100;
			} else {
				newCrop.height = (numValue / imageSize.height) * 100;
			}
			setCrop(newCrop);
		}
	};

	// 处理裁剪位置变化
	const handleCropPositionChange = (field: "x" | "y", value: string) => {
		const numValue = parseInt(value) || 0;
		const maxValue = field === "x" ? imageSize.width - cropSize.width : imageSize.height - cropSize.height;

		if (numValue < 0 || numValue > maxValue) return;

		setCropPosition((prev) => ({ ...prev, [field]: numValue }));

		// 更新裁剪区域
		if (crop && imageSize.width && imageSize.height) {
			const newCrop = { ...crop };
			if (field === "x") {
				newCrop.x = (numValue / imageSize.width) * 100;
			} else {
				newCrop.y = (numValue / imageSize.height) * 100;
			}
			setCrop(newCrop);
		}
	};

	// 处理裁剪图片
	const handleCropImage = (croppedImageUrl: string) => {
		setCroppedImage(croppedImageUrl);
	};

	// 下载裁剪后的图片
	const handleDownload = () => {
		if (!croppedImage) return;

		const link = document.createElement("a");
		link.download = `cropped-${selectedFile?.name || "image"}.png`;
		link.href = croppedImage;
		link.click();
	};

	return (
		<>
			<div className={cn("bg-muted mx-auto rounded-2xl border shadow-lg")}>
				<div className="flex h-full w-full flex-col gap-2 p-2">
					{selectedFile ? (
						<div className="flex min-h-[500px] gap-6">
							<ImageCrop
								aspect={aspectRatio}
								file={selectedFile}
								onChange={handleCropChange}
								onComplete={handleCropComplete}
								onCrop={handleCropImage}
								crop={crop}
							>
								{/* 左侧：图片裁剪区域 */}
								<div className="flex flex-1 flex-col">
									<ImageCropContent className="max-h-[400px] w-full max-w-none" />

									{/* 裁剪后的图片预览 */}
									{croppedImage && (
										<div className="mt-4 rounded-lg border bg-white p-4">
											<h3 className="mb-2 text-sm font-medium">Cropped Result:</h3>
											<img src={croppedImage} alt="Cropped" className="max-h-32 max-w-full rounded border object-contain" />
										</div>
									)}
								</div>

								{/* 右侧：设置面板 */}
								<div className="w-80 space-y-6 rounded-lg bg-white p-4">
									{/* Crop Rectangle 设置 */}
									<div className="space-y-4">
										<h3 className="text-lg font-semibold">Crop Rectangle</h3>

										{/* Aspect Ratio */}
										<div className="space-y-2">
											<Label htmlFor="aspect-ratio">Aspect Ratio</Label>
											<Select value={selectedAspectRatio} onValueChange={handleAspectRatioChange}>
												<SelectTrigger>
													<SelectValue placeholder="Select aspect ratio" />
												</SelectTrigger>
												<SelectContent>
													{ASPECT_RATIOS.map((ar) => (
														<SelectItem key={ar.value} value={ar.value}>
															{ar.label}
														</SelectItem>
													))}
												</SelectContent>
											</Select>
										</div>

										{/* Width & Height */}
										<div className="grid grid-cols-2 gap-3">
											<div className="space-y-2">
												<Label htmlFor="crop-width">Width (px)</Label>
												<Input
													id="crop-width"
													type="number"
													value={cropSize.width}
													onChange={(e) => handleCropSizeChange("width", e.target.value)}
													min={1}
													max={imageSize.width}
												/>
											</div>
											<div className="space-y-2">
												<Label htmlFor="crop-height">Height (px)</Label>
												<Input
													id="crop-height"
													type="number"
													value={cropSize.height}
													onChange={(e) => handleCropSizeChange("height", e.target.value)}
													min={1}
													max={imageSize.height}
												/>
											</div>
										</div>
									</div>

									{/* Crop Position 设置 */}
									<div className="space-y-4">
										<h3 className="text-lg font-semibold">Crop Position</h3>

										<div className="grid grid-cols-2 gap-3">
											<div className="space-y-2">
												<Label htmlFor="crop-x">Position X (px)</Label>
												<Input
													id="crop-x"
													type="number"
													value={cropPosition.x}
													onChange={(e) => handleCropPositionChange("x", e.target.value)}
													min={0}
													max={imageSize.width - cropSize.width}
												/>
											</div>
											<div className="space-y-2">
												<Label htmlFor="crop-y">Position Y (px)</Label>
												<Input
													id="crop-y"
													type="number"
													value={cropPosition.y}
													onChange={(e) => handleCropPositionChange("y", e.target.value)}
													min={0}
													max={imageSize.height - cropSize.height}
												/>
											</div>
										</div>
									</div>

									{/* 操作按钮 */}
									<div className="space-y-3">
										<div className="flex gap-2">
											<ImageCropReset asChild>
												<Button variant="outline" className="flex-1">
													<RotateCcwIcon className="mr-2 size-4" />
													Reset
												</Button>
											</ImageCropReset>

											<ImageCropApply asChild>
												<Button className="flex-1">Crop</Button>
											</ImageCropApply>
										</div>

										{croppedImage && (
											<Button onClick={handleDownload} variant="outline" className="w-full">
												<DownloadIcon className="mr-2 size-4" />
												Download
											</Button>
										)}

										<Button onClick={handleReset} variant="ghost" className="w-full">
											<XIcon className="mr-2 size-4" />
											Select New Image
										</Button>
									</div>
								</div>
							</ImageCrop>
						</div>
					) : (
						<div className="h-full flex-1">
							<Dropzone
								multiple={false}
								maxFiles={1}
								onDrop={handleLocalFileDrop}
								accept={{ "image/*": [] }}
								onError={console.error}
								className={cn(
									"hover:border-primary h-full min-h-[256px] cursor-pointer rounded-xl border-dashed border-zinc-300 bg-white whitespace-pre-wrap hover:bg-white",
								)}
							>
								<DropzoneEmptyState>
									<>
										<div className="bg-muted flex items-center justify-center rounded-full border p-2">
											<ImageIcon className="size-6 text-zinc-700" strokeWidth={1.5} />
										</div>
										<div className="full mt-1 space-y-1 text-sm font-normal">
											<p className={cn(buttonVariants({ size: "lg" }), "mt-2 h-12 bg-blue-500 hover:bg-blue-500/90 has-[>svg]:px-8")}>
												<MousePointerClickIcon className="" />
												Select Image
											</p>
											<p className="w-full text-sm font-normal text-zinc-800">{hookText}</p>
										</div>
									</>
								</DropzoneEmptyState>
								<DropzoneContent />
							</Dropzone>
						</div>
					)}
				</div>
			</div>
		</>
	);
}
